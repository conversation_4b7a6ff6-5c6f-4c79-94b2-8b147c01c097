using GqlPlus.Abstractions.Schema;
using GqlPlus.Ast.Schema.Objects;
using GqlPlus.Modelling;

namespace GqlPlus.Schema.Objects;

public class OutputBaseModelTests(
  IOutputBaseModelChecks checks
) : TestObjBaseModel<IGqlpObjBase<IGqlpObjArg>, IGqlpObjArg, OutputBaseModel>(checks)
{
  [Theory, RepeatData]
  public void Model_EnumArgs(string name, string[] arguments, string enumLabel)
    => checks.ObjBase_Expected(
      checks.ObjBaseAst(name, false, [.. arguments.Select(a => checks.EnumObjArg(a, enumLabel))]),
      checks.ExpectedObjBase(name, false, checks.ExpectedEnumArgs(arguments, enumLabel))
      );
}

internal sealed class OutputBaseModelChecks(
  IModeller<IGqlpObjBase<IGqlpObjArg>, OutputBaseModel> modeller,
  IEncoder<OutputBaseModel> encoding
) : CheckObjBaseModel<IGqlpObjBase<IGqlpObjArg>, IGqlpObjArg, OutputBaseAst, OutputArgAst, OutputBaseModel>(modeller, encoding, TypeKindModel.Output)
  , IOutputBaseModelChecks
{
  public string[] ExpectedEnumArgs(string[] arguments, string enumLabel)
    => [.. ItemsExpected("typeArgs:", arguments,
      a => ["  - !_TypeRef(_SimpleKind)", "    label: " + enumLabel, "    name: " + a, "    typeKind: !_SimpleKind Enum"])];

  protected override OutputBaseAst NewObjBaseAst(string input, bool isTypeParam, IGqlpObjArg[] args)
    => new(AstNulls.At, input) {
      IsTypeParam = isTypeParam,
      Args = args,
    };

  public IGqlpObjArg EnumObjArg(string input, string enumLabel)
    => NewObjArgAst(input, false) with { EnumLabel = enumLabel };

  protected override OutputArgAst NewObjArgAst(string input, bool isTypeParam)
    => new(AstNulls.At, input) {
      IsTypeParam = isTypeParam,
    };
}

public interface IOutputBaseModelChecks
  : ICheckObjBaseModel<IGqlpObjBase<IGqlpObjArg>, IGqlpObjArg, OutputBaseModel>
{
  string[] ExpectedEnumArgs(string[] arguments, string enumLabel);
  IGqlpObjArg EnumObjArg(string input, string enumLabel);
}
