using GqlPlus.Abstractions.Schema;
using GqlPlus.Ast.Schema.Objects;
using GqlPlus.Modelling;

namespace GqlPlus.Schema.Objects;

public class DualBaseModelTests(
  IDualBaseModelChecks checks
) : TestObjBaseModel<IGqlpObjBase<IGqlpObjArg>, IGqlpObjArg, DualBaseModel>(checks)
{ }

internal sealed class DualBaseModelChecks(
  IModeller<IGqlpObjBase<IGqlpObjArg>, DualBaseModel> modeller,
  IEncoder<DualBaseModel> encoding
) : CheckObjBaseModel<IGqlpObjBase<IGqlpObjArg>, IGqlpObjArg, DualBaseAst, DualArgAst, DualBaseModel>(modeller, encoding, TypeKindModel.Dual)
  , IDualBaseModelChecks
{
  protected override DualArgAst NewObjArgAst(string input, bool isTypeParam)
    => new(AstNulls.At, input) {
      IsTypeParam = isTypeParam,
    };

  protected override DualBaseAst NewObjBaseAst(string input, bool isTypeParam, IGqlpObjArg[] args)
    => new(AstNulls.At, input) {
      IsTypeParam = isTypeParam,
      Args = args,
    };
}

public interface IDualBaseModelChecks
  : ICheckObjBaseModel<IGqlpObjBase<IGqlpObjArg>, IGqlpObjArg, DualBaseModel>
{ }
