using GqlPlus.Abstractions.Schema;
using GqlPlus.Ast.Schema.Objects;
using GqlPlus.Parsing;
using GqlPlus.Parsing.Schema.Objects;

namespace GqlPlus.Schema.Objects;

public class ParseOutputBaseTests(
  ICheckObjectBase<IGqlpObjBase<IGqlpObjArg>> checks
) : TestObjectBase<IGqlpObjBase<IGqlpObjArg>>(checks)
{
  [Theory, RepeatData]
  public void WithArgEnumValues_ReturnsCorrectAst(string name, string enumType, string[] enumValues)
    => checks.TrueExpected(
      name + "<" + enumValues.Joined(s => enumType + "." + s) + ">",
      new OutputBaseAst(AstNulls.At, name) with {
        Args = [.. enumValues.Select(enumLabel => new OutputArgAst(AstNulls.At, enumType) with { EnumLabel = enumLabel })]
      });

  [Theory, RepeatData]
  public void WithArgEnumValueBad_ReturnsFalse(string name, string enumType)
    => checks.FalseExpected(name + "<" + enumType + ".>");
}

internal sealed class ParseOutputBaseChecks(
  Parser<IGqlpObjBase<IGqlpObjArg>>.D parser
) : CheckObjectBase<IGqlpObjBase<IGqlpObjArg>, OutputBaseAst, IGqlpObjArg, OutputArgAst>(new OutputFactories(), parser)
{ }
