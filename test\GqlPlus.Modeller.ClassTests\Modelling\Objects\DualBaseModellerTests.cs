﻿namespace GqlPlus.Modelling.Objects;

public class DualBaseModellerTests
  : ModellerClassTestBase<IGqlpObjBase<IGqlpObjArg>, DualBaseModel>
{
  private readonly IModeller<IGqlpObjArg, DualArgModel> _objArg = MFor<IGqlpObjArg, DualArgModel>();

  public DualBaseModellerTests()
    => Modeller = new DualBaseModeller(_objArg);

  protected override IModeller<IGqlpObjBase<IGqlpObjArg>, DualBaseModel> Modeller { get; }

  [Theory, RepeatData]
  public void ToModel_WithValidBase_ReturnsExpectedDualBaseModel(string name, string contents)
  {
    // Arrange
    IGqlpObjBase<IGqlpObjArg> ast = A.Named<IGqlpObjBase<IGqlpObjArg>>(name, contents);
    ast.IsTypeParam.Returns(true);

    // Act
    DualBaseModel result = Modeller.ToModel(ast, TypeKinds);

    // Assert
    result.ShouldNotBeNull()
      .ShouldSatisfyAllConditions(
        r => r.Name.ShouldBe(name),
        r => r.Description.ShouldBe(contents),
        r => r.IsTypeParam.ShouldBeTrue()
      );
  }

  [Theory, RepeatData]
  public void ToModel_WithArgs_ReturnsExpectedDualBaseModel(string name, string argName)
  {
    // Arrange
    IGqlpObjBase<IGqlpObjArg> ast = A.Named<IGqlpObjBase<IGqlpObjArg>>(name);
    IGqlpObjArg arg = A.Named<IGqlpObjArg>(argName);
    ast.Args.Returns([arg]);

    DualArgModel argModel = new(TypeKindModel.Dual, argName, "");
    ToModelReturns(_objArg, argModel);

    // Act
    DualBaseModel result = Modeller.ToModel(ast, TypeKinds);

    // Assert
    result.ShouldNotBeNull()
      .ShouldSatisfyAllConditions(
        r => r.Name.ShouldBe(name),
        r => r.Args.ShouldContain(argModel)
      );
  }
}
