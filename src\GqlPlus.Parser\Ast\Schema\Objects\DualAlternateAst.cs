using GqlPlus.Abstractions.Schema;

namespace GqlPlus.Ast.Schema.Objects;

internal sealed record class DualAlternateAst(
  ITokenAt At,
  string Name,
  string Description
) : AstObjAlternate(At, Name, Description)
  , IGqlpDualAlternate
{
  internal override string Abbr => "DA";
  public override string Label => "Dual";

  public bool Equals(IGqlpObjAlternate<IGqlpObjArg>? other)
    => base.Equals(other as IGqlpObjAlternate);
}
