using GqlPlus.Abstractions.Schema;

namespace GqlPlus.Ast.Schema.Objects;

internal abstract record class AstObjField<TObjBase>(
  ITokenAt At,
  string Name,
  string Description,
  TObjBase Type
) : AstAliased(At, Name, Description)
  , IGqlpObjField
  where TObjBase : IGqlpObjBase
{
  private readonly TObjBase _type = Type;
  public IGqlpModifier[] Modifiers { get; set; } = [];
  public string? EnumLabel { get; set; }

  IGqlpObjType IGqlpObjectEnum.EnumType => _type;
  void IGqlpObjectEnum.SetEnumType(string enumType)
  {
    EnumLabel ??= _type.Name;
    _type.SetName(enumType);
  }

  internal protected IEnumerable<string?> TypeFields(string suffix = "")
    => string.IsNullOrWhiteSpace(EnumLabel)
        ? [":", .. _type.GetFields(), .. Modifiers.AsString(), suffix]
        : ["=", .. _type.GetFields(), "." + EnumLabel];

  public string ModifiedType => _type.GetFields().Skip(2).Concat(Modifiers.AsString()).Joined();

  IEnumerable<IGqlpModifier> IGqlpModifiers.Modifiers => Modifiers;
  IGqlpObjBase IGqlpObjField.Type => _type;

  public virtual bool Equals(AstObjField<TObjBase>? other)
    => other is IGqlpObjField field && Equals(field);
  public bool Equals(IGqlpObjField? other)
    => base.Equals(other)
    && _type.Equals(other!.Type)
    && Modifiers.SequenceEqual(other.Modifiers)
    && EnumLabel == other.EnumLabel;
  public override int GetHashCode()
    => HashCode.Combine(base.GetHashCode(), _type, Modifiers.Length, EnumLabel);
}
