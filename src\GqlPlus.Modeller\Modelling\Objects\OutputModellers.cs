﻿namespace GqlPlus.Modelling.Objects;

internal class OutputModeller(
  ObjectModellers<IGqlpObjBase<IGqlpObjArg>, IGqlpOutputField, IGqlpOutputAlternate, OutputBaseModel, OutputFieldModel, OutputAlternateModel> modellers
) : ModellerObject<IGqlpOutputObject, IGqlpObjBase<IGqlpObjArg>, IGqlpOutputField, IGqlpOutputAlternate, TypeOutputModel, OutputBaseModel, OutputFieldModel, OutputAlternateModel>(TypeKindModel.Output, modellers)
{
  protected override TypeOutputModel ToModel(IGqlpOutputObject ast, IMap<TypeKindModel> typeKinds)
    => new(ast.Name, ast.Description) {
      Aliases = [.. ast.Aliases],
      Parent = ParentModel(ast.ObjParent, typeKinds),
      TypeParams = TypeParamsModels(ast.TypeParams, typeKinds),
      Fields = FieldsModels(ast.ObjFields, typeKinds),
      Alternates = AlternatesModels(ast.ObjAlternates, typeKinds),
    };
}

internal class OutputArgModeller
  : ModellerObjArg<IGqlpObjArg, OutputArgModel>
{
  protected override OutputArgModel ToModel(IGqlpObjArg ast, IMap<TypeKindModel> typeKinds)
      => string.IsNullOrWhiteSpace(ast.EnumLabel)
      ? typeKinds.TryGetValue(ast.Name, out TypeKindModel typeKind) && typeKind == TypeKindModel.Dual
        ? new(typeKind, "", "") {
          Dual = new DualArgModel(typeKind, ast.Name, ast.Description) {
            IsTypeParam = ast.IsTypeParam,
          }
        }
        : new(typeKind, ast.Name, ast.Description) {
          IsTypeParam = ast.IsTypeParam,
        }
      : new(TypeKindModel.Enum, ast.Name, ast.Description) { EnumLabel = ast.EnumLabel };
}

internal class OutputBaseModeller(
  IModeller<IGqlpObjArg, OutputArgModel> objArg
) : ModellerObjBase<IGqlpObjBase<IGqlpObjArg>, IGqlpObjArg, OutputBaseModel, OutputArgModel>(objArg)
{
  protected override OutputBaseModel ToModel(IGqlpObjBase<IGqlpObjArg> ast, IMap<TypeKindModel> typeKinds)
    => typeKinds.TryGetValue(ast.Name, out TypeKindModel typeKind) && typeKind == TypeKindModel.Dual
    ? new("", ast.Description) {
      IsTypeParam = ast.IsTypeParam,
      Dual = new DualBaseModel(ast.Name, ast.Description) {
        IsTypeParam = ast.IsTypeParam,
        Args = [.. ast.Args.Select(a => new DualArgModel(
          typeKinds.TryGetValue(a.Name, out TypeKindModel argTypeKind) ? argTypeKind : TypeKindModel.Dual,
          a.Name, a.Description) {
          IsTypeParam = a.IsTypeParam,
        })],
      }
    }
    : new(ast.Name, ast.Description) {
      IsTypeParam = ast.IsTypeParam,
      Args = ModelArgs(ast, typeKinds),
    };
}

internal class OutputFieldModeller(
  IModifierModeller modifier,
  IModeller<IGqlpInputParam, InputParamModel> parameter,
  IModeller<IGqlpObjBase<IGqlpObjArg>, OutputBaseModel> objBase
) : ModellerObjField<IGqlpObjBase<IGqlpObjArg>, IGqlpOutputField, OutputBaseModel, OutputFieldModel>(modifier, objBase)
{
  protected override OutputFieldModel FieldModel(IGqlpOutputField field, OutputBaseModel type, IMap<TypeKindModel> typeKinds)
    => string.IsNullOrWhiteSpace(field.EnumLabel)
      ? new(field.Name, type, field.Description) {
        Params = parameter.ToModels(field.Params, typeKinds),
      }
      : new(field.Name, type, field.Description) { // or should it be `type`
        Enum = new(field.Name, type.Name, field.EnumLabel!, type.Description)
      };
}

internal class OutputAlternateModeller(
  IModeller<IGqlpModifier, CollectionModel> collection,
  IModeller<IGqlpObjBase<IGqlpObjArg>, OutputBaseModel> objBase
) : ModellerObjAlternate<IGqlpObjBase<IGqlpObjArg>, IGqlpOutputAlternate, OutputBaseModel, OutputAlternateModel>(collection, objBase)
{
  protected override OutputAlternateModel AlternateModel(OutputBaseModel type)
    => new(type);
}
