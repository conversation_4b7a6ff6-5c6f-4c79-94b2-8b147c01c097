﻿namespace GqlPlus.Modelling.Objects;

internal class InputModeller(
  ObjectModellers<IGqlpObjBase<IGqlpObjArg>, IGqlpInputField, IGqlpInputAlternate, InputBaseModel, InputFieldModel, InputAlternateModel> modellers
) : ModellerObject<IGqlpInputObject, IGqlpObjBase<IGqlpObjArg>, IGqlpInputField, IGqlpInputAlternate, TypeInputModel, InputBaseModel, InputFieldModel, InputAlternateModel>(TypeKindModel.Input, modellers)
{
  protected override TypeInputModel ToModel(IGqlpInputObject ast, IMap<TypeKindModel> typeKinds)
    => new(ast.Name, ast.Description) {
      Aliases = [.. ast.Aliases],
      Parent = ParentModel(ast.ObjParent, typeKinds),
      TypeParams = TypeParamsModels(ast.TypeParams, typeKinds),
      Fields = FieldsModels(ast.ObjFields, typeKinds),
      Alternates = AlternatesModels(ast.ObjAlternates, typeKinds),
    };
}

internal class InputArgModeller
  : ModellerObjArg<IGqlpObjArg, InputArgModel>
{
  protected override InputArgModel ToModel(IGqlpObjArg ast, IMap<TypeKindModel> typeKinds)
    => typeKinds.TryGetValue(ast.Name, out TypeKindModel typeKind) && typeKind == TypeKindModel.Dual
    ? new(TypeKindModel.Dual, "", ast.Description) {
      Dual = new DualArgModel(typeKind, ast.Name, ast.Description) {
        IsTypeParam = ast.IsTypeParam,
      }
    }
    : new(TypeKindModel.Input, ast.Name, ast.Description) {
      IsTypeParam = ast.IsTypeParam,
    };
}

internal class InputBaseModeller(
  IModeller<IGqlpObjArg, InputArgModel> objArg
) : ModellerObjBase<IGqlpObjBase<IGqlpObjArg>, IGqlpObjArg, InputBaseModel, InputArgModel>(objArg)
{
  protected override InputBaseModel ToModel(IGqlpObjBase<IGqlpObjArg> ast, IMap<TypeKindModel> typeKinds)
    => typeKinds.TryGetValue(ast.Name, out TypeKindModel typeKind) && typeKind == TypeKindModel.Dual
    ? new("", ast.Description) {
      Dual = new DualBaseModel(ast.Name, ast.Description) {
        IsTypeParam = ast.IsTypeParam,
        Args = [.. ast.Args.Select(a => new DualArgModel(
          typeKinds.TryGetValue(a.Name, out TypeKindModel argTypeKind) ? argTypeKind : TypeKindModel.Dual,
          a.Name, a.Description) {
          IsTypeParam = a.IsTypeParam,
        })],
      }
    }
    : new(ast.Name, ast.Description) {
      IsTypeParam = ast.IsTypeParam,
      Args = ModelArgs(ast, typeKinds),
    };
}

internal class InputFieldModeller(
  IModifierModeller modifier,
  IModeller<IGqlpObjBase<IGqlpObjArg>, InputBaseModel> objBase,
  IModeller<IGqlpConstant, ConstantModel> constant
) : ModellerObjField<IGqlpObjBase<IGqlpObjArg>, IGqlpInputField, InputBaseModel, InputFieldModel>(modifier, objBase)
{
  protected override InputFieldModel FieldModel(IGqlpInputField ast, InputBaseModel type, IMap<TypeKindModel> typeKinds)
    => new(ast.Name, type with { Description = ast.BaseType.Description }, ast.Description) {
      Default = constant.TryModel(ast.DefaultValue, typeKinds),
    };
}

internal class InputAlternateModeller(
  IModeller<IGqlpModifier, CollectionModel> collection,
  IModeller<IGqlpObjBase<IGqlpObjArg>, InputBaseModel> objBase
) : ModellerObjAlternate<IGqlpObjBase<IGqlpObjArg>, IGqlpInputAlternate, InputBaseModel, InputAlternateModel>(collection, objBase)
{
  protected override InputAlternateModel AlternateModel(InputBaseModel type)
    => new(type);
}

internal class InputParamModeller(
  IModifierModeller modifier,
  IModeller<IGqlpConstant, ConstantModel> constant
) : ModellerBase<IGqlpInputParam, InputParamModel>
{
  protected override InputParamModel ToModel(IGqlpInputParam ast, IMap<TypeKindModel> typeKinds)
  {
    InputParamModel model = new(ast.Type.Name, ast.Description) {
      IsTypeParam = ast.Type.IsTypeParam,
      Modifiers = modifier.ToModels<ModifierModel>(ast.Modifiers, typeKinds),
      DefaultValue = constant.TryModel(ast.DefaultValue, typeKinds),
    };
    return model;
  }
}
